import * as THREE from 'three';
import { FontLoader } from 'three/examples/jsm/loaders/FontLoader.js';
import { TextGeometry } from 'three/examples/jsm/geometries/TextGeometry.js';
import { SceneManager } from '../SceneManager';
import { ScrollTracker, type ScrollData, lerp, createRandomColor, createThickGrid, getRandomFloat } from '../utils';

interface Primitive {
	mesh: THREE.Mesh;
	originalPosition: THREE.Vector3;
	rotationSpeed: THREE.Vector3;
	floatOffset: number;
	floatSpeed: number;
}

export class RotatingPrimitivesScene {
	private sceneManager: SceneManager;
	private scrollTracker: ScrollTracker;
	private primitives: Primitive[] = [];
	private sol = new THREE.Group();
	private ambientLight: THREE.AmbientLight;
	private directionalLight: THREE.DirectionalLight;
	private fontLoader: FontLoader;
	private textMesh: THREE.Mesh | null = null;
	private time = 0;

	constructor(canvas: HTMLCanvasElement) {
		this.sceneManager = new SceneManager({
			canvas,
			alpha: true,
			antialias: true,
			enableOrbitControls: false
		});

		this.scrollTracker = new ScrollTracker();
		this.fontLoader = new FontLoader();
		this.setupLights();
		// this.createHelpers();
		this.createPrimitives();
		this.createPlane();
		this.createMountains();
		this.createSun();
		this.create3DText();

		// Position camera
		this.sceneManager.camera.position.set(0, 2.5, 5);
		this.sceneManager.camera.lookAt(0, 0, -25);

		// Update controls if they exist
		if (this.sceneManager.controls) {
			this.sceneManager.controls.update();
		}

		this.setupScrollInteraction();
		this.startAnimation();
	}

	private createHelpers(): void {
		const gridHelper = new THREE.GridHelper(100, 100);
		gridHelper.material.color = new THREE.Color(0x084c61);
		gridHelper.material.linewidth = 10;
		// gridHelper.rotation.x = -Math.PI / 2;
		this.sceneManager.addToScene(gridHelper);
	}

	private createPlane(): void {
		// Create a wireframe plane
		const geometry = createThickGrid(50, 50, 25, 0.15, 0xa6cfd5);
		geometry.position.z = -15;
		this.sceneManager.addToScene(geometry);
	}

	private async create3DText(): Promise<void> {
		try {
			// Load a font (using Three.js built-in font)
			const font = await this.fontLoader.loadAsync('https://threejs.org/examples/fonts/helvetiker_regular.typeface.json');

			// Create text geometry
			const textGeometry = new TextGeometry('    Mario Cabrera\nCreative Developer', {
				font: font,
				size: 0.8,
				depth: 0.2,
				curveSegments: 12,
				bevelEnabled: true,
				bevelThickness: 0.03,
				bevelSize: 0.02,
				bevelOffset: 0,
				bevelSegments: 5
			});

			// Center the text geometry
			textGeometry.computeBoundingBox();
			const textWidth = textGeometry.boundingBox!.max.x - textGeometry.boundingBox!.min.x;
			const textHeight = textGeometry.boundingBox!.max.y - textGeometry.boundingBox!.min.y;
			textGeometry.translate(-textWidth / 2, -textHeight / 2, 0);

			// Create material with a nice color
			const textMaterial = new THREE.MeshLambertMaterial({
				color: 0x2191FB
			});

			// Create mesh and position it
			this.textMesh = new THREE.Mesh(textGeometry, textMaterial);
			this.textMesh.position.set(0, 5, -10);

			// Add to scene
			this.sceneManager.addToScene(this.textMesh);

			console.log('3D text "Creative Developer" created successfully');

		} catch (error) {
			console.error('Error loading font or creating 3D text:', error);
			// Fallback: create a simple text placeholder using a basic geometry
			this.createFallbackText();
		}
	}

	private createFallbackText(): void {
		// Simple fallback using a box geometry as placeholder
		const geometry = new THREE.BoxGeometry(4, 0.5, 0.2);
		const material = new THREE.MeshLambertMaterial({
			color: 0x3b82f6,
			transparent: true,
			opacity: 0.8
		});

		this.textMesh = new THREE.Mesh(geometry, material);
		this.textMesh.position.set(0, 5, -10);
		this.sceneManager.addToScene(this.textMesh);

		console.log('Fallback text placeholder created');
	}

	private setupLights(): void {
		// Ambient light for overall illumination
		this.ambientLight = new THREE.AmbientLight(0xF6F6F6, 0.9);
		this.sceneManager.addToScene(this.ambientLight);

		// Directional light for shadows and depth
		this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
		this.directionalLight.position.set(5, 5, 5);
		this.directionalLight.castShadow = true;
		this.sceneManager.addToScene(this.directionalLight);
	}

	private createSun(){
		// sol
		const solRadio = 25;
		const solZ = -90;
		const solY = 20;
		this.sol.position.set(0, solY, solZ);
		let geometry = new THREE.DodecahedronGeometry( solRadio, 1 );
		let material: any = new THREE.MeshBasicMaterial({
			color: 0xdb504a,
			wireframe: true
		});
		let mesh = new THREE.Mesh(geometry, material);
		this.sol.add(mesh);

		geometry = new THREE.DodecahedronGeometry( solRadio, 1 );
		material = new THREE.MeshLambertMaterial({
			color: 0xdb504a,
			transparent: true,
			opacity: 0.3,
			emissive: 0xdb504a,
			emissiveIntensity: 2.5
		});
		mesh = new THREE.Mesh(geometry, material);
		this.sol.add(mesh);

		const glowMaterial = new THREE.MeshBasicMaterial({
			color: 0xdb504a,
			side: THREE.BackSide,
			transparent: true,
			opacity: 0.2
		});
		const glow = new THREE.Mesh(new THREE.DodecahedronGeometry(solRadio + 1, 1), glowMaterial);
		this.sol.add(glow);

		const intensity = 150000;
		const light = new THREE.PointLight(0xdb504a, intensity);
		light.position.set(0, solY, solZ);
		this.sceneManager.addToScene(light);
		this.sceneManager.addToScene(this.sol);
	}

	private createMountains(): void {
		this.sceneManager.renderer.localClippingEnabled = true;

		let height = 9;
		let radius = 15;
		let segments = 10;
		let geometry: any = new THREE.CylinderGeometry( radius/5, radius, height, segments );
		let material = new THREE.MeshLambertMaterial({
			color: 0xD9AC81,
			flatShading: true
		});
		let mesh = new THREE.Mesh(geometry, material);
		mesh.position.set(-15, height / 2, -40);
		this.sceneManager.addToScene(mesh);

		height = 3;
		radius = 5;
		segments = 10;
		geometry = new THREE.CylinderGeometry( radius/5, radius, height, segments );
		material = new THREE.MeshLambertMaterial({
			color: 0xD9AC81,
			flatShading: true,
		});
		mesh = new THREE.Mesh(geometry, material);
		mesh.position.set(10, height / 2, -36);
		this.sceneManager.addToScene(mesh);

		height = 13;
		radius = 20;
		segments = 6;
		geometry = new THREE.CylinderGeometry( radius/6, radius, height, segments );
		material = new THREE.MeshLambertMaterial({
			color: 0xD9AC81,
			flatShading: true,
		});
		mesh = new THREE.Mesh(geometry, material);
		mesh.position.set(0, height / 2, -55);
		this.sceneManager.addToScene(mesh);

		height = 8;
		radius = 15;
		segments = 9;
		geometry = new THREE.ConeGeometry(  radius, height, segments );
		material = new THREE.MeshLambertMaterial({
			color: 0xD9AC81,
			flatShading: true,
		});
		mesh = new THREE.Mesh(geometry, material);
		mesh.position.set(20, height / 2, -50);
		this.sceneManager.addToScene(mesh);

		height = 12;
		radius = 10;
		segments = 5;
		geometry = new THREE.ConeGeometry( radius, height, segments );
		material = new THREE.MeshLambertMaterial({
			color: 0xD9AC81,
			flatShading: true,
		});
		mesh = new THREE.Mesh(geometry, material);
		mesh.position.set(-12, height / 2, -60);
		this.sceneManager.addToScene(mesh);
	}

	private createPrimitives(): void {
		const geometries = [
			new THREE.BoxGeometry(1, 1, 1),
			new THREE.SphereGeometry(0.6, 32, 32),
			new THREE.CylinderGeometry(0.5, 0.5, 1, 8),
			new THREE.OctahedronGeometry(0.7),
			new THREE.DodecahedronGeometry(0.6),
			new THREE.IcosahedronGeometry(0.7)
		];

		const numPrimitives = 25;

		for (let i = 0; i < numPrimitives; i++) {
			const geometry = geometries[Math.floor(Math.random() * geometries.length)];
			const material = new THREE.MeshLambertMaterial({
				color: createRandomColor(),
				transparent: true,
				opacity: 0.8
			});

			const mesh = new THREE.Mesh(geometry, material);

			// Random position
			const x = getRandomFloat(-50, 50);
			const y = getRandomFloat(5, 25);
			const z = getRandomFloat(-20, -50);

			mesh.position.set(x, y, z);

			// Random scale
			const scale = 0.5 + Math.random() * 1.5;
			mesh.scale.setScalar(scale);

			const primitive: Primitive = {
				mesh,
				originalPosition: mesh.position.clone(),
				rotationSpeed: new THREE.Vector3(
					(Math.random() - 0.5) * 0.02,
					(Math.random() - 0.5) * 0.02,
					(Math.random() - 0.5) * 0.02
				),
				floatOffset: Math.random() * Math.PI * 2,
				floatSpeed: 0.5 + Math.random() * 0.5
			};

			this.primitives.push(primitive);
			this.sceneManager.addToScene(mesh);
		}
	}

	private setupScrollInteraction(): void {
		this.scrollTracker.addCallback((data: ScrollData) => {
			// Move camera based on scroll
			const targetZ = data.scrollProgress * 25;
			this.sceneManager.camera.position.z = lerp(
				this.sceneManager.camera.position.z,
				targetZ,
				0.1
			);

			// other option
			// const t = document.body.getBoundingClientRect().top;
			// console.log(t * -0.02);
			// this.sceneManager.camera.position.z = t * -0.02;

			// Rotate primitives based on scroll
			this.primitives.forEach((primitive, index) => {
				const scrollRotation = data.scrollProgress * Math.PI * 2;
				const offset = (index / this.primitives.length) * Math.PI * 2;

				primitive.mesh.rotation.y = scrollRotation + offset;

				// Move primitives slightly based on scroll
				const scrollOffset = data.scrollProgress * 2;
				primitive.mesh.position.x = primitive.originalPosition.x + Math.sin(scrollRotation + offset) * scrollOffset;
				primitive.mesh.position.z = primitive.originalPosition.z + Math.cos(scrollRotation + offset) * scrollOffset;
			});
		});
	}

	private startAnimation(): void {
		this.sceneManager.startAnimation((time: number) => {
			this.time = time * 0.001; // Convert to seconds

			// Animate primitives
			this.primitives.forEach((primitive) => {
				// Continuous rotation
				primitive.mesh.rotation.x += primitive.rotationSpeed.x;
				primitive.mesh.rotation.y += primitive.rotationSpeed.y;
				primitive.mesh.rotation.z += primitive.rotationSpeed.z;

				// Floating animation
				const floatY = Math.sin(this.time * primitive.floatSpeed + primitive.floatOffset) * 0.5;
				primitive.mesh.position.y = primitive.originalPosition.y + floatY;

				// Subtle pulsing scale
				const pulseScale = 1 + Math.sin(this.time * 2 + primitive.floatOffset) * 0.005;
				primitive.mesh.scale.setScalar(primitive.mesh.scale.x * pulseScale);
			});

			// Animate lights
			this.directionalLight.position.x = Math.sin(this.time * 0.5) * 5;
			this.directionalLight.position.z = Math.cos(this.time * 0.5) * 5;

			// Animate text if it exists
			if (this.textMesh) {
				// Gentle rotation around Y axis
				this.textMesh.rotation.y = Math.sin(this.time * 0.3) * 0.1;

				// Subtle floating animation
				this.textMesh.position.y = 5 + Math.sin(this.time * 0.8) * 0.2;

				// Slight scale pulsing
				const scale = 1 + Math.sin(this.time * 1.2) * 0.05;
				this.textMesh.scale.setScalar(scale);
			}

			// Animate sun
			this.sol.rotation.z = this.time * 0.15;
			this.sol.rotation.y = this.time * 0.25;
			this.sol.rotation.x = this.time * 0.005;
		});
	}

	public destroy(): void {
		this.sceneManager.destroy();
		this.scrollTracker.destroy();

		// Dispose of geometries and materials
		this.primitives.forEach((primitive) => {
			primitive.mesh.geometry.dispose();
			if (Array.isArray(primitive.mesh.material)) {
				primitive.mesh.material.forEach(material => material.dispose());
			} else {
				primitive.mesh.material.dispose();
			}
		});

		// Dispose of text mesh
		if (this.textMesh) {
			this.textMesh.geometry.dispose();
			if (Array.isArray(this.textMesh.material)) {
				this.textMesh.material.forEach(material => material.dispose());
			} else {
				this.textMesh.material.dispose();
			}
		}
	}
}
