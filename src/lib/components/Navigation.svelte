<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { m } from '$lib/paraglide/messages.js';
	import LanguageSwitcher from './LanguageSwitcher.svelte';

	let scrollY = 0;
	let isScrolled = false;
	let isMobileMenuOpen = false;

	$: navItems = [
		{ href: '/', label: m.navigation_home() },
		{ href: '/projects', label: m.navigation_projects() },
		{ href: '/about', label: m.navigation_about() },
		{ href: '/skills', label: m.navigation_skills() },
		{ href: '/contact', label: m.navigation_contact() }
	];

	$: isScrolled = scrollY > 50;
	$: currentPath = $page.url.pathname;

	onMount(() => {
		const handleScroll = () => {
			scrollY = window.scrollY;
		};

		window.addEventListener('scroll', handleScroll, { passive: true });
		return () => window.removeEventListener('scroll', handleScroll);
	});

	const handleNavClick = (href: string) => {
		isMobileMenuOpen = false;
		goto(href);
	};

	const toggleMobileMenu = () => {
		isMobileMenuOpen = !isMobileMenuOpen;
	};
</script>

<svelte:window bind:scrollY />

<nav
	class="bg-gray-900/90 fixed top-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out"
	class:shadow-lg={isScrolled}
	class:py-4={!isScrolled}
	class:py-1={isScrolled}
>
	<div class="container mx-auto px-4 sm:px-6 lg:px-8">
		<div class="flex items-center justify-between">
			<!-- Logo -->
			<button
				on:click={() => handleNavClick('/')}
				class="text-2xl font-bold text-white hover:text-blue-400 transition-colors duration-200"
			>
				<img src="images/logo.png" alt="Logo" class="h-9" />
			</button>

			<!-- Desktop Navigation -->
			<div class="hidden md:flex items-center space-x-8">
				{#each navItems as item}
					<button
						on:click={() => handleNavClick(item.href)}
						class="relative text-white hover:text-blue-400 transition-colors duration-200 group"
						class:text-blue-400={currentPath === item.href}
					>
						{item.label}
						<span
							class="absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-400 transition-all duration-200 group-hover:w-full"
							class:w-full={currentPath === item.href}
						></span>
					</button>
				{/each}
			</div>

			<!-- Language Switcher (Desktop) -->
			<div class="hidden md:block">
				<LanguageSwitcher />
			</div>

			<!-- Mobile Menu Button -->
			<button
				on:click={toggleMobileMenu}
				class="md:hidden text-white hover:text-blue-400 transition-colors duration-200"
				aria-label={m.navigation_toggle_menu()}
			>
				<svg
					class="w-6 h-6 transition-transform duration-200"
					class:rotate-90={isMobileMenuOpen}
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
				>
					{#if !isMobileMenuOpen}
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
					{:else}
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					{/if}
				</svg>
			</button>
		</div>

		<!-- Mobile Navigation -->
		<div
			class="md:hidden overflow-hidden transition-all duration-300 ease-in-out"
			class:max-h-0={!isMobileMenuOpen}
			class:max-h-96={isMobileMenuOpen}
			class:opacity-0={!isMobileMenuOpen}
			class:opacity-100={isMobileMenuOpen}
		>
			<div class="py-4 space-y-2">
				{#each navItems as item}
					<button
						on:click={() => handleNavClick(item.href)}
						class="block w-full text-left px-4 py-2 text-white hover:text-blue-400 hover:bg-gray-700/60 rounded-lg transition-all duration-200"
						class:text-blue-400={currentPath === item.href}
						class:bg-gray-700={currentPath === item.href}
						class:bg-opacity-60={currentPath === item.href}
					>
						{item.label}
					</button>
				{/each}
			</div>
		</div>
	</div>
</nav>

<style>
	nav {
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
	}
</style>
