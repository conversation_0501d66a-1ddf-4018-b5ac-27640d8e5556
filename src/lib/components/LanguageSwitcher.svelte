<script lang="ts">
	import { onMount } from 'svelte';
	import { getLocale, setLocale, locales } from '$lib/paraglide/runtime';
	import { m } from '$lib/paraglide/messages.js';

	let currentLocale = getLocale();
	let isOpen = false;

	// Language options with display names
	const languageOptions = [
		{ code: 'en', name: 'English', flag: '🇺🇸' },
		{ code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' }
	];

	// Get current language display info
	$: currentLanguage = languageOptions.find(lang => lang.code === currentLocale) || languageOptions[0];

	// Detect user's preferred language on first visit
	onMount(() => {
		// Check if this is the first visit (no locale preference stored)
		const hasStoredPreference = document.cookie.includes('PARAGLIDE_LOCALE=');
		
		if (!hasStoredPreference && typeof navigator !== 'undefined') {
			// Get user's preferred language from browser
			const userLang = navigator.language.toLowerCase();
			
			// Check if user's language matches any of our supported locales
			const supportedLang = locales.find(locale => 
				userLang.startsWith(locale) || userLang === locale
			);
			
			if (supportedLang && supportedLang !== currentLocale) {
				// Set the detected language without reloading
				setLocale(supportedLang, { reload: false });
				currentLocale = supportedLang;
			}
		}
	});

	const toggleLanguage = () => {
		// Simple toggle between English and Spanish
		const newLocale = currentLocale === 'en' ? 'es' : 'en';
		setLocale(newLocale, { reload: false });
		currentLocale = newLocale;
		isOpen = false;
	};

	const selectLanguage = (langCode: string) => {
		if (langCode !== currentLocale) {
			setLocale(langCode, { reload: false });
			currentLocale = langCode;
		}
		isOpen = false;
	};

	// Close dropdown when clicking outside
	const handleClickOutside = (event: MouseEvent) => {
		const target = event.target as HTMLElement;
		if (!target.closest('.language-switcher')) {
			isOpen = false;
		}
	};

	onMount(() => {
		document.addEventListener('click', handleClickOutside);
		return () => {
			document.removeEventListener('click', handleClickOutside);
		};
	});
</script>

<!-- Language Switcher Button -->
<div class="language-switcher relative">
	<!-- Globe Icon Button -->
	<button
		on:click={toggleLanguage}
		class="flex items-center space-x-2 text-white hover:text-blue-400 transition-colors duration-200 p-2 rounded-lg hover:bg-gray-700/60"
		aria-label={m.navigation_language_switcher()}
		title={m.navigation_language_switcher()}
	>
		<!-- Globe Icon -->
		<svg 
			class="w-5 h-5" 
			fill="none" 
			stroke="currentColor" 
			viewBox="0 0 24 24"
			aria-hidden="true"
		>
			<path 
				stroke-linecap="round" 
				stroke-linejoin="round" 
				stroke-width="2" 
				d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
			/>
		</svg>
		
		<!-- Current Language Code -->
		<span class="text-sm font-medium uppercase hidden sm:inline">
			{currentLanguage.code}
		</span>
		
		<!-- Flag (visible on mobile) -->
		<span class="text-lg sm:hidden" aria-hidden="true">
			{currentLanguage.flag}
		</span>
	</button>

	<!-- Dropdown Menu (for future expansion to more languages) -->
	{#if isOpen}
		<div 
			class="absolute right-0 top-full mt-2 bg-gray-800/95 backdrop-blur-sm rounded-lg shadow-lg border border-gray-700 min-w-[160px] z-50"
			role="menu"
			aria-orientation="vertical"
		>
			{#each languageOptions as lang}
				<button
					on:click={() => selectLanguage(lang.code)}
					class="w-full flex items-center space-x-3 px-4 py-3 text-left text-white hover:bg-gray-700/60 transition-colors duration-200 first:rounded-t-lg last:rounded-b-lg"
					class:bg-gray-700={lang.code === currentLocale}
					class:bg-opacity-60={lang.code === currentLocale}
					role="menuitem"
				>
					<span class="text-lg" aria-hidden="true">{lang.flag}</span>
					<span class="flex-1">{lang.name}</span>
					{#if lang.code === currentLocale}
						<svg class="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
						</svg>
					{/if}
				</button>
			{/each}
		</div>
	{/if}
</div>

<style>
	.language-switcher {
		/* Ensure proper stacking context */
		z-index: 60;
	}
</style>
