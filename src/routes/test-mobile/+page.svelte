<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';
	import { getMobileViewportInfo, optimizeCanvasForMobile, getOptimalPixelRatio } from '$lib/three/MobileCanvasUtils';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;
	let viewportInfo: any = null;
	let debugInfo = {
		userAgent: '',
		screenDimensions: '',
		viewportDimensions: '',
		devicePixelRatio: 0,
		optimalPixelRatio: 0,
		isMobile: false,
		hasNotch: false
	};

	onMount(async () => {
		if (browser && canvasElement) {
			// Get viewport info for debugging
			viewportInfo = getMobileViewportInfo();
			
			// Update debug info
			debugInfo = {
				userAgent: navigator.userAgent,
				screenDimensions: `${screen.width}x${screen.height}`,
				viewportDimensions: `${viewportInfo.width}x${viewportInfo.height}`,
				devicePixelRatio: window.devicePixelRatio,
				optimalPixelRatio: getOptimalPixelRatio(),
				isMobile: viewportInfo.isMobile,
				hasNotch: viewportInfo.hasNotch
			};

			// Optimize canvas for mobile
			optimizeCanvasForMobile(canvasElement);

			// Create a simple Three.js scene for testing
			const { RotatingPrimitivesScene } = await import('$lib/three/scenes/RotatingPrimitivesScene');
			scene = new RotatingPrimitivesScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});

	function refreshViewportInfo() {
		if (browser) {
			viewportInfo = getMobileViewportInfo();
			debugInfo = {
				...debugInfo,
				viewportDimensions: `${viewportInfo.width}x${viewportInfo.height}`,
				devicePixelRatio: window.devicePixelRatio,
				optimalPixelRatio: getOptimalPixelRatio(),
				isMobile: viewportInfo.isMobile,
				hasNotch: viewportInfo.hasNotch
			};
		}
	}
</script>

<svelte:head>
	<title>Mobile Canvas Test - Portfolio</title>
	<meta name="description" content="Testing mobile canvas optimizations" />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="canvas-background"
></canvas>

<!-- Debug Information Overlay -->
<div class="fixed top-4 left-4 right-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono max-h-96 overflow-y-auto">
	<h2 class="text-lg font-bold mb-4 text-center">Mobile Canvas Debug Info</h2>
	
	<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
		<div>
			<h3 class="font-bold text-yellow-400 mb-2">Device Information</h3>
			<p><strong>User Agent:</strong> {debugInfo.userAgent}</p>
			<p><strong>Screen:</strong> {debugInfo.screenDimensions}</p>
			<p><strong>Viewport:</strong> {debugInfo.viewportDimensions}</p>
			<p><strong>Device Pixel Ratio:</strong> {debugInfo.devicePixelRatio}</p>
			<p><strong>Optimal Pixel Ratio:</strong> {debugInfo.optimalPixelRatio}</p>
			<p><strong>Is Mobile:</strong> {debugInfo.isMobile ? 'Yes' : 'No'}</p>
			<p><strong>Has Notch:</strong> {debugInfo.hasNotch ? 'Yes' : 'No'}</p>
		</div>
		
		{#if viewportInfo}
		<div>
			<h3 class="font-bold text-green-400 mb-2">Viewport Details</h3>
			<p><strong>Width:</strong> {viewportInfo.width}px</p>
			<p><strong>Height:</strong> {viewportInfo.height}px</p>
			<p><strong>Orientation:</strong> {viewportInfo.isPortrait ? 'Portrait' : 'Landscape'}</p>
			<p><strong>Safe Area Top:</strong> {viewportInfo.safeAreaInsets.top}px</p>
			<p><strong>Safe Area Bottom:</strong> {viewportInfo.safeAreaInsets.bottom}px</p>
			<p><strong>Safe Area Left:</strong> {viewportInfo.safeAreaInsets.left}px</p>
			<p><strong>Safe Area Right:</strong> {viewportInfo.safeAreaInsets.right}px</p>
		</div>
		{/if}
	</div>
	
	<div class="mt-4 text-center">
		<button 
			on:click={refreshViewportInfo}
			class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-white font-bold"
		>
			Refresh Info
		</button>
	</div>
</div>

<!-- Test Content -->
<div class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-4xl mx-auto text-center text-white">
		<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8 mb-8">
			<h1 class="text-4xl md:text-6xl font-bold mb-6">
				<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
					Mobile Canvas Test
				</span>
			</h1>
			<p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
				This page tests mobile canvas optimizations. The canvas should cover the full viewport 
				without any visible edges, especially when scrolling on mobile devices.
			</p>
		</div>

		<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8 mb-8">
			<h2 class="text-2xl font-bold mb-4 text-blue-400">Test Instructions</h2>
			<div class="text-left space-y-4">
				<p><strong>1. Scroll Test:</strong> Scroll up and down to ensure the canvas covers the full viewport without showing edges.</p>
				<p><strong>2. Orientation Test:</strong> Rotate your device to test landscape/portrait transitions.</p>
				<p><strong>3. Flickering Test:</strong> Watch for any canvas flickering or size changes during scrolling.</p>
				<p><strong>4. Performance Test:</strong> Check if the 3D scene runs smoothly without frame drops.</p>
			</div>
		</div>

		<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8 mb-8">
			<h2 class="text-2xl font-bold mb-4 text-green-400">Expected Behavior</h2>
			<div class="text-left space-y-4">
				<p>✅ Canvas covers entire viewport without gaps</p>
				<p>✅ No flickering during scroll or orientation changes</p>
				<p>✅ Smooth 3D animation performance</p>
				<p>✅ Proper handling of safe areas on devices with notches</p>
				<p>✅ Responsive to viewport changes</p>
			</div>
		</div>

		<!-- Add some content to enable scrolling -->
		<div class="space-y-8">
			{#each Array(10) as _, i}
				<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-6">
					<h3 class="text-xl font-bold mb-2 text-purple-400">Test Section {i + 1}</h3>
					<p class="text-gray-300">
						This is test content to enable scrolling. The canvas should remain stable 
						and cover the full viewport as you scroll through this content.
					</p>
				</div>
			{/each}
		</div>

		<div class="mt-12">
			<a 
				href="/"
				class="inline-block px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105"
			>
				Back to Home
			</a>
		</div>
	</div>
</div>
