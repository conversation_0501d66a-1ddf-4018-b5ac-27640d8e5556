<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';
	import { m } from '$lib/paraglide/messages.js';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;
	let scrollContainer: HTMLElement;

	// Create reactive projects array that updates with language changes
	$: projects = [
		{
			id: 1,
			title: m.project_1_title(),
			description: m.project_1_description(),
			technologies: ['Unity 3D', 'C#', 'JavaScript', 'Blender', 'Python'],
			image: 'images/MetaBonesScreenshot.jpg',
			demoUrl: 'https://marioc9955.github.io/VeterinariaVirtual/',
			featured: true
		},
		{
			id: 2,
			title: m.project_2_title(),
			description: m.project_2_description(),
			technologies: ['Three.js', 'Svelte', 'WebGL', 'GLSL', 'Vite', 'Netlify'],
			image: 'images/3DPortfolioScreenshot.jpg',
			demoUrl: 'https://mario-cabrera-portfolio.netlify.app/',
			featured: true
		},
		{
			id: 3,
			title: m.project_3_title(),
			description: m.project_3_description(),
			technologies: ['Unity 3D', 'C#', 'Unity Machine Learning Agents', 'Blender'],
			image: 'images/ChaosBallScreenshot.jpg',
			demoUrl: 'https://marioc9955.itch.io/chaos-ball',
			githubUrl: 'https://github.com/Marioc9955/Training3DCannon',
			featured: true
		},
		{
			id: 4,
			title: m.project_4_title(),
			description: m.project_4_description(),
			technologies: ['React', 'Node.js', 'Next.js', 'TypeScript', 'Vercel'],
			image: 'images/RanchScreenshot.jpg',
			demoUrl: 'https://www.ranchodortiz.com/',
			featured: false
		},
		{
			id: 5,
			title: m.project_5_title(),
			description: 'A sleek, service focused website for a nail and spa salon, featuring a homepage, service listings, ' +
				'and a contact section. The appointments page integrates with the client’s Square Appointment system, ' +
				'making it easy for customers to book online, and for the client to edit her services.',
			technologies: ['Svelte', 'Node.js', 'TypeScript', 'Netlify'],
			image: 'images/BBScreenshot.jpg',
			demoUrl: 'https://bbnailsandspasalon.com/',
			featured: false
		},
		{
			id: 6,
			title: m.project_6_title(),
			description: m.project_6_description(),
			technologies: ['Unity 3D', 'C#', 'JavaScript'],
			image: 'images/GuangopoloScreenshot.jpg',
			demoUrl: 'https://marioc9955.itch.io/guangopolo-el-primer-cedazo',
			featured: false
		},
		{
			id: 7,
			title: m.project_7_title(),
			description: m.project_7_description(),
			technologies: ['Unity 3D', 'C#'],
			image: 'images/ParadiseShroomsScrenshoot.png',
			demoUrl: 'https://marioc9955.itch.io/paradise-shrooms',

		},
		{
			id: 8,
			title: m.project_8_title(),
			description: m.project_8_description(),
			technologies: ['Unity 3D', 'C#'],
			image: 'images/ProjectAlkawingScrenshoot.png',
			demoUrl: 'https://marioc9955.itch.io/project-alkawing',
		},
		{
			id: 9,
			title: m.project_9_title(),
			description: m.project_9_description(),
			technologies: ['Java', 'OpenGL'],
			image: 'images/ProjectArwingScreenshot.jpg',
			demoUrl: 'https://www.youtube.com/watch?v=hVJ9-Ra71UE',
			githubUrl: 'https://github.com/Marioc9955/ProjectArwingFR',
		}
	];

	onMount(async () => {
		if (browser && canvasElement) {
			const { ParticleScene } = await import('$lib/three/scenes/ParticleScene');
			scene = new ParticleScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});

	const scrollLeft = () => {
		scrollContainer.scrollBy({ left: -400, behavior: 'smooth' });
	};

	const scrollRight = () => {
		scrollContainer.scrollBy({ left: 400, behavior: 'smooth' });
	};
</script>

<svelte:head>
	<title>{m.projects_title()}</title>
	<meta name="description" content={m.projects_meta_description()} />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- Projects Section -->
<section class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-7xl mx-auto">
		<!-- Header -->
		<div class="text-center text-white mb-16">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-8 mb-8">
				<h1 class="text-4xl md:text-6xl font-bold mb-6">
					<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
						{m.projects_page_title()}
					</span>
				</h1>
				<p class="text-xl max-w-3xl mx-auto">
					{m.projects_page_subtitle()}
				</p>
			</div>
		</div>

		<!-- Featured Projects -->
		<div class="mb-16">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-6 mb-8">
				<h2 class="text-2xl font-bold text-white flex items-center">
					<span class="w-2 h-8 bg-blue-400 mr-4"></span>
					{m.projects_featured()}
				</h2>
			</div>

			<div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
				{#each projects.filter(p => p.featured) as project}
					<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-gray-700/60 transition-all duration-300 transform hover:scale-105">
						<div class="aspect-video bg-gray-800 overflow-hidden">
							<img
								src={project.image}
								alt={project.title}
								class="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
							/>
						</div>
						<div class="p-6">
							<h3 class="text-xl font-bold text-white mb-2">{project.title}</h3>
							<p class="text-gray-300 mb-4 text-sm">{project.description}</p>
							<div class="flex flex-wrap gap-2 mb-4">
								{#each project.technologies as tech}
									<span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full">
										{tech}
									</span>
								{/each}
							</div>
							<div class="flex gap-3">
								<a
									href={project.demoUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="px-4 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
								>
									{m.projects_view_demo()}
								</a>
								<a
									href={project.githubUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="px-4 py-2 border border-gray-400 text-gray-300 text-sm rounded hover:bg-gray-400 hover:text-black transition-colors"
								>
									{m.projects_view_code()}
								</a>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- All Projects - Horizontal Scroll -->
		<div class="mb-16">
			<div class="flex items-center justify-between mb-8">
				<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-4">
					<h2 class="text-2xl font-bold text-white flex items-center">
						<span class="w-2 h-8 bg-purple-400 mr-4"></span>
						{m.projects_all()}
					</h2>
				</div>
				<div class="flex gap-2">
					<button
						on:click={scrollLeft}
						class="p-2 bg-gray-800/60 text-white rounded-full hover:bg-gray-700/60 transition-colors"
						aria-label={m.projects_scroll_left()}
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
						</svg>
					</button>
					<button
						on:click={scrollRight}
						class="p-2 bg-gray-800/60 text-white rounded-full hover:bg-gray-700/60 transition-colors"
						aria-label={m.projects_scroll_right()}
					>
						<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
						</svg>
					</button>
				</div>
			</div>

			<div
				bind:this={scrollContainer}
				class="flex gap-6 overflow-x-auto scrollbar-hide pb-4"
				style="scroll-snap-type: x mandatory;"
			>
				{#each projects as project}
					<div class="flex-none w-80 bg-gray-800/60 backdrop-blur-sm rounded-lg overflow-hidden hover:bg-gray-700/60 transition-all duration-300" style="scroll-snap-align: start;">
						<div class="aspect-video bg-gray-800 overflow-hidden">
							<img
								src={project.image}
								alt={project.title}
								class="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
							/>
						</div>
						<div class="p-6">
							<h3 class="text-lg font-bold text-white mb-2">{project.title}</h3>
							<p class="text-gray-300 mb-4 text-sm line-clamp-3">{project.description}</p>
							<div class="flex flex-wrap gap-1 mb-4">
								{#each project.technologies.slice(0, 3) as tech}
									<span class="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full">
										{tech}
									</span>
								{/each}
								{#if project.technologies.length > 3}
									<span class="px-2 py-1 bg-gray-500/20 text-gray-300 text-xs rounded-full">
										+{project.technologies.length - 3}
									</span>
								{/if}
							</div>
							<div class="flex gap-2">
								<a
									href={project.demoUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="flex-1 px-3 py-2 bg-blue-500 text-white text-sm rounded text-center hover:bg-blue-600 transition-colors"
								>
									{m.projects_demo()}
								</a>
								<a
									href={project.githubUrl}
									target="_blank"
									rel="noopener noreferrer"
									class="flex-1 px-3 py-2 border border-gray-400 text-gray-300 text-sm rounded text-center hover:bg-gray-400 hover:text-black transition-colors"
								>
									{m.projects_code()}
								</a>
							</div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Call to Action -->
		<div class="text-center">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-8 mb-8">
				<h3 class="text-2xl font-bold text-white mb-4">{m.projects_cta_title()}</h3>
				<p class="text-gray-300 max-w-2xl mx-auto">
					{m.projects_cta_description()}
				</p>
			</div>
			<a
				href="/contact"
				class="inline-block px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
			>
				{m.projects_cta_button()}
			</a>
		</div>
	</div>
</section>

<style>
	.scrollbar-hide {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}

	.scrollbar-hide::-webkit-scrollbar {
		display: none;
	}

	.line-clamp-3 {
		display: -webkit-box;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}
</style>
