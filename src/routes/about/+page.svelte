<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { browser } from '$app/environment';
	import { m } from '$lib/paraglide/messages.js';

	let canvasElement: HTMLCanvasElement;
	let scene: any = null;

	const technologies = [
		{ name: 'JavaScript/TypeScript', category: 'Languages', level: 95 },
		{ name: 'Python', category: 'Languages', level: 75 },
		{ name: 'Java', category: 'Languages', level: 80 },
		{ name: 'SvelteKit', category: 'Frontend', level: 90 },
		{ name: 'React/Next.js', category: 'Frontend', level: 88 },
		{ name: 'C#', category: 'Languages', level: 75 },
		{ name: 'Unity 3D', category: 'Graphics', level: 80 },
		{ name: 'Three.js/WebGL', category: 'Graphics', level: 65 },
		{ name: 'Node.js', category: 'Backend', level: 87 },
		{ name: 'OracleSQL', category: 'Database', level: 80 },
		{ name: 'Docker', category: 'DevOps', level: 75 },
		{ name: 'Git/GitHub', category: 'DevOps', level: 80 }
	];

	// Create reactive experiences array that updates with language changes
	$: experiences = [
		{
			year: m.about_exp_1_year(),
			title: m.about_exp_1_title(),
			company: m.about_exp_1_company(),
			description: m.about_exp_1_description()
		},
		{
			year: m.about_exp_2_year(),
			title: m.about_exp_2_title(),
			company: m.about_exp_2_company(),
			description: m.about_exp_2_description()
		},
		{
			year: m.about_exp_3_year(),
			title: m.about_exp_3_title(),
			company: m.about_exp_3_company(),
			description: m.about_exp_3_description()
		}
	];

	onMount(async () => {
		if (browser && canvasElement) {
			const { AvatarScene } = await import('$lib/three/scenes/AvatarScene');
			scene = new AvatarScene(canvasElement);
		}
	});

	onDestroy(() => {
		if (scene) {
			scene.destroy();
		}
	});
</script>

<svelte:head>
	<title>{m.about_title()}</title>
	<meta name="description" content={m.about_meta_description()} />
</svelte:head>

<!-- 3D Background Canvas -->
<canvas
	bind:this={canvasElement}
	class="fixed top-0 left-0 w-full h-full -z-10"
	style="pointer-events: none;"
></canvas>

<!-- About Section -->
<section class="relative min-h-screen pt-24 pb-20 px-4 sm:px-6 lg:px-8">
	<div class="max-w-6xl mx-auto">
		<!-- Header -->
		<div class="text-center text-white mb-16">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-8 mb-8">
				<h1 class="text-4xl md:text-6xl font-bold mb-6">
					<span class="bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent">
						{m.about_page_title()}
					</span>
				</h1>
				<p class="text-xl max-w-3xl mx-auto">
					{m.about_page_subtitle()}
				</p>
			</div>
		</div>

		<!-- Story Section -->
		<div class="grid lg:grid-cols-2 gap-12 mb-20">
			<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8">
				<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
					<span class="w-2 h-8 bg-blue-400 mr-4"></span>
					{m.about_journey_title()}
				</h2>
				<div class="text-gray-300 space-y-4">
					<p>
						{m.about_journey_p1()}
					</p>
					<p>
						{m.about_journey_p2()}
					</p>
					<p>
						{m.about_journey_p3()}
					</p>
				</div>
			</div>

			<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8">
				<h2 class="text-2xl font-bold text-white mb-6 flex items-center">
					<span class="w-2 h-8 bg-purple-400 mr-4"></span>
					{m.about_drives_title()}
				</h2>
				<div class="text-gray-300 space-y-4">
					<p>
						{m.about_drives_p1()}
					</p>
					<p>
						{m.about_drives_p2()}
					</p>
					<p>
						{m.about_drives_p3()}
					</p>
				</div>
			</div>
		</div>

		<!-- Experience Timeline -->
		<div class="mb-20">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-6 mb-8">
				<h2 class="text-2xl font-bold text-white flex items-center">
					<span class="w-2 h-8 bg-cyan-400 mr-4"></span>
					{m.about_experience_title()}
				</h2>
			</div>

			<div class="space-y-8">
				{#each experiences as exp, index}
					<div class="flex gap-6 group">
						<div class="flex-shrink-0 w-24 text-right">
							<span class="text-blue-400 font-semibold text-sm">{exp.year}</span>
						</div>
						<div class="flex-shrink-0 w-4 flex flex-col items-center">
							<div class="w-4 h-4 bg-blue-400 rounded-full group-hover:bg-purple-400 transition-colors"></div>
							{#if index < experiences.length - 1}
								<div class="w-0.5 h-16 bg-gray-600 mt-2"></div>
							{/if}
						</div>
						<div class="flex-1 bg-gray-800/60 backdrop-blur-sm rounded-lg p-6 group-hover:bg-gray-700/60 transition-all">
							<h3 class="text-xl font-bold text-white mb-1">{exp.title}</h3>
							<p class="text-blue-400 mb-3">{exp.company}</p>
							<p class="text-gray-300">{exp.description}</p>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Technologies -->
		<div class="mb-20">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-6 mb-8">
				<h2 class="text-2xl font-bold text-white flex items-center">
					<span class="w-2 h-8 bg-green-400 mr-4"></span>
					{m.about_technologies_title()}
				</h2>
			</div>

			<div class="grid md:grid-cols-2 gap-6">
				{#each technologies as tech}
					<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-4 hover:bg-gray-700/60 transition-all">
						<div class="flex justify-between items-center mb-2">
							<span class="text-white font-medium">{tech.name}</span>
							<span class="text-blue-400 text-sm">{tech.category}</span>
						</div>
						<div class="w-full bg-gray-700 rounded-full h-2">
							<div
								class="bg-gradient-to-r from-blue-400 to-purple-500 h-2 rounded-full transition-all duration-1000"
								style="width: {tech.level}%"
							></div>
						</div>
					</div>
				{/each}
			</div>
		</div>

		<!-- Personal Interests -->
		<div class="bg-gray-800/60 backdrop-blur-sm rounded-lg p-8">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-6 mb-6">
				<h2 class="text-2xl font-bold text-white flex items-center">
					<span class="w-2 h-8 bg-yellow-400 mr-4"></span>
					{m.about_beyond_title()}
				</h2>
			</div>

			<div class="grid md:grid-cols-3 gap-6 text-gray-300">
				<div class="text-center">
					<div class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
							<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
						</svg>
					</div>
					<h3 class="font-semibold text-white mb-2">{m.about_3d_graphics()}</h3>
					<p class="text-sm">{m.about_3d_graphics_desc()}</p>
				</div>

				<div class="text-center">
					<div class="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
							<path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
						</svg>
					</div>
					<h3 class="font-semibold text-white mb-2">{m.about_open_source()}</h3>
					<p class="text-sm">{m.about_open_source_desc()}</p>
				</div>

				<div class="text-center">
					<div class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-green-400" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
						</svg>
					</div>
					<h3 class="font-semibold text-white mb-2">{m.about_learning()}</h3>
					<p class="text-sm">{m.about_learning_desc()}</p>
				</div>
			</div>
		</div>

		<!-- Call to Action -->
		<div class="text-center mt-16">
			<div class="bg-gray-700/40 backdrop-blur-sm rounded-lg p-8 mb-8">
				<h3 class="text-2xl font-bold text-white mb-4">{m.about_cta_title()}</h3>
				<p class="text-gray-300 max-w-2xl mx-auto">
					{m.about_cta_description()}
				</p>
			</div>
			<a
				href="/contact"
				class="inline-block px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg"
			>
				{m.about_cta_button()}
			</a>
		</div>
	</div>
</section>
