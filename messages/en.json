{"$schema": "https://inlang.com/schema/inlang-message-format", "hello_world": "Hello, {name} from en!", "navigation": {"home": "Home", "projects": "Projects", "about": "About", "skills": "Skills", "contact": "Contact", "portfolio": "Portfolio", "toggle_menu": "Toggle mobile menu", "language_switcher": "Switch language"}, "home": {"title": "Portfolio - Creative Developer", "meta_description": "Welcome to my portfolio. I'm a passionate developer creating innovative solutions with modern technologies.", "hero_title": "Computer Graphics Engineer & Software Developer", "hero_subtitle": "I bring ideas to life with code, creativity, and performance in mind.", "explore_projects": "Explore My Projects", "learn_more": "Learn More About Me", "intro_title": "Passionate About Innovation", "intro_description": "I specialize in creating immersive digital experiences that combine cutting-edge technology with creative design. From interactive 3D web applications to robust backend systems, I love turning complex ideas into elegant solutions.", "creative_developer_3d": "<PERSON>\\nCreative Developer"}, "projects": {"title": "Projects - Portfolio", "meta_description": "Explore my portfolio of innovative projects including web applications, 3D experiences, and software solutions.", "page_title": "My Projects", "page_subtitle": "A showcase of my work in web development, 3D graphics, and software engineering", "view_demo": "View Demo", "view_code": "View Code", "featured": "Featured"}, "about": {"title": "About - Portfolio", "meta_description": "Learn more about my background, experience, and passion for technology and creative development.", "page_title": "About Me", "page_subtitle": "Passionate developer with a love for creating innovative digital experiences", "bio_title": "Who I Am", "bio_text": "I'm a dedicated software developer with a passion for creating innovative digital experiences. My journey in technology has led me through various domains, from web development to 3D graphics programming. I believe in the power of clean code, creative problem-solving, and continuous learning.", "experience_title": "Professional Experience", "technologies_title": "Technologies I Work With", "languages": "Languages", "frontend": "Frontend", "backend": "Backend", "graphics": "Graphics", "database": "Database", "devops": "DevOps"}, "skills": {"title": "Skills - Portfolio", "meta_description": "Discover my technical skills and expertise in web development, 3D graphics, and software engineering.", "page_title": "Skills & Expertise", "page_subtitle": "A comprehensive overview of my technical abilities and areas of expertise", "technical_skills": "Technical Skills", "soft_skills": "Soft Skills", "currently_exploring": "Currently Exploring", "webassembly_desc": "WebAssembly (WASM) for high-performance web apps", "threejs_desc": "Advanced Three.js techniques and shaders", "ml_desc": "Machine Learning integration in web applications", "edge_desc": "Edge computing and serverless architectures"}, "contact": {"title": "Contact - Portfolio", "meta_description": "Get in touch with me for collaboration opportunities, project inquiries, or just to say hello.", "page_title": "Get In Touch", "page_subtitle": "Let's collaborate and create something amazing together", "form_title": "Send me a message", "name_label": "Name", "name_placeholder": "Your name", "email_label": "Email", "email_placeholder": "<EMAIL>", "subject_label": "Subject", "subject_placeholder": "What's this about?", "message_label": "Message", "message_placeholder": "Tell me about your project or just say hello...", "send_button": "Send Message", "sending": "Sending...", "success_message": "Message sent successfully! I'll get back to you soon.", "error_message": "Failed to send message. Please try again.", "connect_title": "Let's Connect", "social_links": "Find me on social media", "contact_info": "Contact Information", "phone": "+****************", "location": "Your City, Country"}, "footer": {"brand_description": "Passionate developer creating innovative solutions with modern technologies. Let's build something amazing together.", "quick_links": "Quick Links", "contact_info": "Contact Info", "built_with": "Built with ❤️ using SvelteKit & Three.js", "rights_reserved": "All rights reserved"}}