{"$schema": "https://inlang.com/schema/inlang-message-format", "hello_world": "Hello, {name} from en!", "navigation_home": "Home", "navigation_projects": "Projects", "navigation_about": "About", "navigation_skills": "Skills", "navigation_contact": "Contact", "navigation_portfolio": "Portfolio", "navigation_toggle_menu": "Toggle mobile menu", "navigation_language_switcher": "Switch language", "home_title": "Portfolio - Creative Developer", "home_meta_description": "Welcome to my portfolio. I'm a passionate developer creating innovative solutions with modern technologies.", "home_hero_title": "Computer Graphics Engineer & Software Developer", "home_hero_subtitle": "I bring ideas to life with code, creativity, and performance in mind.", "home_explore_projects": "Explore My Projects", "home_learn_more": "Learn More About Me", "home_intro_title": "Passionate About Innovation", "home_intro_description": "I create immersive, interactive experiences using 3D graphics, modern web tech, and thoughtful design.", "home_creative_developer_3d": "    <PERSON>\\nCreative Developer", "home_user_centered_design": "User-Centered Design", "home_user_centered_desc": "Creating intuitive and engaging user experiences that delight and inspire.", "home_modern_technologies": "Modern Technologies", "home_modern_technologies_desc": "Leveraging the latest tools and frameworks to build scalable solutions.", "home_quality_focused": "Quality Focused", "home_quality_focused_desc": "Committed to delivering high-quality, performant, and maintainable code.", "home_lets_work_together": "Let's Work Together", "projects_title": "Projects - Portfolio", "projects_meta_description": "Explore my portfolio of innovative projects including web applications, 3D experiences, and software solutions.", "projects_page_title": "My Projects", "projects_page_subtitle": "Here's a selection of projects I've developed, from interactive web apps to immersive 3D experiences. Each one reflects my passion for building creative, performant software using modern tools and thoughtful design. Each project represents a unique challenge and learning experience.", "projects_view_demo": "Live Demo", "projects_view_code": "GitHub", "projects_featured": "Featured Projects", "projects_all": "All Projects", "projects_demo": "Demo", "projects_code": "Code", "projects_scroll_left": "<PERSON><PERSON> left", "projects_scroll_right": "<PERSON><PERSON> right", "project_1_title": "Virtual Veterinary: <PERSON><PERSON>", "project_1_description": "An interactive educational tool built with Unity 3D to explore the canine skeletal system. I led a 3-person team, automated 3D model processing with Python, and developed C# mechanics for navigation, animations, and exploration. All optimized for web.", "project_2_title": "3D Portfolio Website", "project_2_description": "An immersive portfolio website showcasing Three.js capabilities with interactive 3D elements, particle systems, and scroll-based animations.", "project_3_title": "Geospatial Data Visualization Platform", "project_3_description": "An interactive web application for exploring geographic data. Users can toggle map layers on and off, while internal users can upload new spatial data via a GeoServer-powered backend. Designed for flexibility and ease of use in managing multi-layer geographic content.", "project_4_title": "Rancho D' Ortiz – Ranch Business Website", "project_4_description": "A clean, multi page business site built with Next.js for a ranch. Includes home, about, mission, products, and services pages. The products and services are displayed with Swiper sliders for a smooth, responsive browsing experience.", "project_5_title": "BB Nails and Spa Salon – Appointment Driven Website", "project_5_description": "A sleek, service focused website for a nail and spa salon, featuring a homepage, service listings, and a contact section. The appointments page integrates with the client's Square Appointment system, making it easy for customers to book online, and for the client to edit her services.", "project_6_title": "Guangopolo: El Primer <PERSON> – Legend Inspired 2D Game", "project_6_description": "A culturally themed 2D game developed in Unity to bring a local legend to life. I led a 4-person team using Scrum and designed multiple levels with unique gameplay mechanics, all implemented in C#.", "project_7_title": "<PERSON><PERSON> – Minimax AI Implementation", "project_7_description": "A classic Tic Tac Toe game featuring an unbeatable AI opponent powered by the Minimax algorithm. Built with vanilla JavaScript, HTML, and CSS, this project demonstrates fundamental AI concepts and game theory in a clean, interactive interface.", "project_8_title": "Project Alkawing – Endless Procedural Space Survival", "project_8_description": "A fast-paced 2D space game built in Unity, where players pilot a spaceship through an endless, procedurally generated universe. Collect energy cells to stay alive, dodge or destroy asteroids, and survive as long as you can in deep space.", "project_9_title": "Project Arwing FR – 3D Procedural World in Java & OpenGL", "project_9_description": "An academic project focused on real-time 3D navigation through a procedurally generated space environment. Developed using Java and OpenGL, featuring a fully controllable spaceship and dynamic world generation techniques.", "about_title": "About - Portfolio", "about_meta_description": "Learn more about my background, experience, and passion for technology and creative development.", "about_page_title": "About Me", "about_page_subtitle": "Passionate developer with a love for creating innovative digital experiences", "about_bio_title": "Who I Am", "about_bio_text": "I'm a dedicated software developer with a passion for creating innovative digital experiences. My journey in technology has led me through various domains, from web development to 3D graphics programming. I believe in the power of clean code, creative problem-solving, and continuous learning.", "about_experience_title": "Professional Experience", "about_technologies_title": "Technologies I Work With", "about_languages": "Languages", "about_frontend": "Frontend", "about_backend": "Backend", "about_graphics": "Graphics", "about_database": "Database", "about_devops": "DevOps", "skills_title": "Skills - Portfolio", "skills_meta_description": "Discover my technical skills and expertise in web development, 3D graphics, and software engineering.", "skills_page_title": "Skills & Expertise", "skills_page_subtitle": "A comprehensive overview of my technical abilities and areas of expertise", "skills_technical_skills": "Technical Skills", "skills_soft_skills": "Soft Skills", "skills_currently_exploring": "Currently Exploring", "skills_webassembly_desc": "WebAssembly (WASM) for high-performance web apps", "skills_threejs_desc": "Advanced Three.js techniques and shaders", "skills_ml_desc": "Machine Learning integration in web applications", "skills_edge_desc": "Edge computing and serverless architectures", "contact_title": "Contact - Portfolio", "contact_meta_description": "Get in touch with me for collaboration opportunities, project inquiries, or just to say hello.", "contact_page_title": "Get In Touch", "contact_page_subtitle": "Let's collaborate and create something amazing together", "contact_form_title": "Send me a message", "contact_name_label": "Name", "contact_name_placeholder": "Your name", "contact_email_label": "Email", "contact_email_placeholder": "<EMAIL>", "contact_subject_label": "Subject", "contact_subject_placeholder": "What's this about?", "contact_message_label": "Message", "contact_message_placeholder": "Tell me about your project or just say hello...", "contact_send_button": "Send Message", "contact_sending": "Sending...", "contact_success_message": "Message sent successfully! I'll get back to you soon.", "contact_error_message": "Failed to send message. Please try again.", "contact_connect_title": "Let's Connect", "contact_social_links": "Find me on social media", "contact_contact_info": "Contact Information", "contact_phone": "+****************", "contact_location": "Your City, Country", "footer_brand_description": "Passionate developer creating innovative solutions with modern technologies. Let's build something amazing together.", "footer_quick_links": "Quick Links", "footer_contact_info": "Contact Info", "footer_built_with": "Built with ❤️ using SvelteKit & Three.js", "footer_rights_reserved": "All rights reserved"}