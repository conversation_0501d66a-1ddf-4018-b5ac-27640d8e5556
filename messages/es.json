{"$schema": "https://inlang.com/schema/inlang-message-format", "hello_world": "¡<PERSON><PERSON>, {name} desde es!", "navigation_home": "<PERSON><PERSON>o", "navigation_projects": "Proyectos", "navigation_about": "Acerca de", "navigation_skills": "Habilidades", "navigation_contact": "Contacto", "navigation_portfolio": "Portafolio", "navigation_toggle_menu": "Alternar menú móvil", "navigation_language_switcher": "Cambiar idioma", "home_title": "Portafolio - Desarrollador Creativo", "home_meta_description": "Bienvenido a mi portafolio. Soy un desarrollador apasionado que crea soluciones innovadoras con tecnologías modernas.", "home_hero_title": "Ingeniero en Gráficos por Computadora y Desarrollador de Software", "home_hero_subtitle": "Doy vida a las ideas con código, creatividad y rendimiento en mente.", "home_explore_projects": "Explorar Mis Proyectos", "home_learn_more": "<PERSON><PERSON><PERSON> Mí", "home_intro_title": "Apasionado por la Innovación", "home_intro_description": "Creo experiencias inmersivas e interactivas usando gráficos 3D, tecnología web moderna y diseño reflexivo.", "home_creative_developer_3d": "      <PERSON>\\nDesarrollador Creativo", "home_user_centered_design": "Diseño Centrado en el Usuario", "home_user_centered_desc": "Creando experiencias de usuario intuitivas y atractivas que deleitan e inspiran.", "home_modern_technologies": "Tecnologías Modernas", "home_modern_technologies_desc": "Aprovechando las herramientas y frameworks más recientes para construir soluciones escalables.", "home_quality_focused": "Enfocado en la Calidad", "home_quality_focused_desc": "Comprometido a entregar código de alta calidad, eficiente y mantenible.", "home_lets_work_together": "Trabaj<PERSON><PERSON>", "projects_title": "Proyectos - Portafolio", "projects_meta_description": "Explora mi portafolio de proyectos innovadores incluyendo aplicaciones web, experiencias 3D y soluciones de software.", "projects_page_title": "Mis Proyectos", "projects_page_subtitle": "Una muestra de mi trabajo en desarrollo web, gráficos 3D e ingeniería de software", "projects_view_demo": "Ver Demo", "projects_view_code": "Ver Código", "projects_featured": "Destacado", "about_title": "Acerca de - Portafolio", "about_meta_description": "Conoce más sobre mi experiencia, antecedentes y pasión por la tecnología y el desarrollo creativo.", "about_page_title": "Acerca de Mí", "about_page_subtitle": "Desarrollador apasionado con amor por crear experiencias digitales innovadoras", "about_bio_title": "<PERSON><PERSON><PERSON>", "about_bio_text": "Soy un desarrollador de software dedicado con pasión por crear experiencias digitales innovadoras. Mi viaje en la tecnología me ha llevado a través de varios dominios, desde el desarrollo web hasta la programación de gráficos 3D. Creo en el poder del código limpio, la resolución creativa de problemas y el aprendizaje continuo.", "about_experience_title": "Experiencia Profesional", "about_technologies_title": "Tecnologías con las que Trabajo", "about_languages": "Lenguajes", "about_frontend": "Frontend", "about_backend": "Backend", "about_graphics": "Grá<PERSON><PERSON>", "about_database": "Base de Datos", "about_devops": "DevOps", "skills_title": "Habilidades - Portafolio", "skills_meta_description": "Descubre mis habilidades técnicas y experiencia en desarrollo web, gráficos 3D e ingeniería de software.", "skills_page_title": "Habilidades y Experiencia", "skills_page_subtitle": "Una visión integral de mis habilidades técnicas y áreas de especialización", "skills_technical_skills": "Habilidades Técnicas", "skills_soft_skills": "Habilidades Blandas", "skills_currently_exploring": "Explorando Actualmente", "skills_webassembly_desc": "WebAssembly (WASM) para aplicaciones web de alto rendimiento", "skills_threejs_desc": "Técnicas avanzadas de Three.js y shaders", "skills_ml_desc": "Integración de Machine Learning en aplicaciones web", "skills_edge_desc": "Computación en el borde y arquitecturas serverless", "contact_title": "Contacto - Portafolio", "contact_meta_description": "Ponte en contacto conmigo para oportunidades de colaboración, consultas de proyectos o simplemente para saludar.", "contact_page_title": "Ponte en Contacto", "contact_page_subtitle": "Colaboremos y creemos algo increíble juntos", "contact_form_title": "Envíame un mensaje", "contact_name_label": "Nombre", "contact_name_placeholder": "Tu nombre", "contact_email_label": "Correo electrónico", "contact_email_placeholder": "<EMAIL>", "contact_subject_label": "<PERSON><PERSON><PERSON>", "contact_subject_placeholder": "¿De qué se trata?", "contact_message_label": "Men<PERSON><PERSON>", "contact_message_placeholder": "Cuéntame sobre tu proyecto o simplemente saluda...", "contact_send_button": "<PERSON><PERSON><PERSON>", "contact_sending": "Enviando...", "contact_success_message": "¡Mensaje enviado exitosamente! Te responderé pronto.", "contact_error_message": "Error al enviar el mensaje. Por favor, inténtalo de nuevo.", "contact_connect_title": "Conectemos", "contact_social_links": "Encuéntrame en redes sociales", "contact_contact_info": "Información de Contacto", "contact_phone": "+****************", "contact_location": "Tu Ciudad, País", "footer_brand_description": "Desarrollador apasionado creando soluciones innovadoras con tecnologías modernas. Construyamos algo increíble juntos.", "footer_quick_links": "<PERSON><PERSON><PERSON>", "footer_contact_info": "Información de Contacto", "footer_built_with": "<PERSON><PERSON> con ❤️ usando SvelteKit y Three.js", "footer_rights_reserved": "Todos los derechos reservados"}