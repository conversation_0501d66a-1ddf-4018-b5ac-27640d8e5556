{"$schema": "https://inlang.com/schema/inlang-message-format", "hello_world": "¡<PERSON><PERSON>, {name} desde es!", "navigation_home": "<PERSON><PERSON>o", "navigation_projects": "Proyectos", "navigation_about": "Acerca de", "navigation_skills": "Habilidades", "navigation_contact": "Contacto", "navigation_portfolio": "Portafolio", "navigation_toggle_menu": "Alternar menú móvil", "navigation_language_switcher": "Cambiar idioma", "home_title": "Portafolio - Desarrollador Creativo", "home_meta_description": "Bienvenido a mi portafolio. Soy un desarrollador apasionado que crea soluciones innovadoras con tecnologías modernas.", "home_hero_title": "Ingeniero en Computación Gráfica y Desarrollador de Software", "home_hero_subtitle": "Doy vida a las ideas con código, creatividad y rendimiento en mente.", "home_explore_projects": "Explorar Mis Proyectos", "home_learn_more": "<PERSON><PERSON><PERSON> Mí", "home_intro_title": "Apasionado por la Innovación", "home_intro_description": "Creo experiencias inmersivas e interactivas usando gráficos 3D, tecnología web moderna y diseño reflexivo.", "home_creative_developer_3d": "      <PERSON>\\nDesarrollador Creativo", "home_user_centered_design": "Diseño Centrado en el Usuario", "home_user_centered_desc": "Creando experiencias de usuario intuitivas y atractivas que deleitan e inspiran.", "home_modern_technologies": "Tecnologías Modernas", "home_modern_technologies_desc": "Aprovechando las herramientas y frameworks más recientes para construir soluciones escalables.", "home_quality_focused": "Enfocado en la Calidad", "home_quality_focused_desc": "Comprometido a entregar código de alta calidad, eficiente y mantenible.", "home_lets_work_together": "Trabaj<PERSON><PERSON>", "projects_title": "Proyectos - Portafolio", "projects_meta_description": "Explora mi portafolio de proyectos innovadores incluyendo aplicaciones web, experiencias 3D y soluciones de software.", "projects_page_title": "Mis Proyectos", "projects_page_subtitle": "Aquí tienes una selección de proyectos que he desarrollado, desde aplicaciones web interactivas hasta experiencias 3D inmersivas. Cada uno refleja mi pasión por construir software creativo y eficiente usando herramientas modernas y diseño reflexivo. Cada proyecto representa un desafío único y una experiencia de aprendizaje.", "projects_view_demo": "Demo en Vivo", "projects_view_code": "GitHub", "projects_featured": "Proyectos Destacados", "projects_all": "Todos los Proyectos", "projects_demo": "Demo", "projects_code": "Código", "projects_scroll_left": "<PERSON><PERSON><PERSON><PERSON>", "projects_scroll_right": "<PERSON><PERSON><PERSON><PERSON> der<PERSON>a", "projects_cta_title": "¿Interesado en Trabajar Juntos?", "projects_cta_description": "Siempre estoy emocionado de asumir nuevos desafíos y colaborar en proyectos innovadores. Hablemos sobre cómo podemos dar vida a tus ideas.", "projects_cta_button": "Ponte en Contacto", "project_1_title": "Veterinaria Virtual: <PERSON><PERSON>", "project_1_description": "Una herramienta educativa interactiva construida con Unity 3D para explorar el sistema esquelético canino. Lideré un equipo de 3 personas, automaticé el procesamiento de modelos 3D con Python, y desarrollé mecánicas en C# para navegación, animaciones y exploración. Todo optimizado para web.", "project_2_title": "Sitio Web de Portafolio 3D", "project_2_description": "Un sitio web de portafolio inmersivo que muestra las capacidades de Three.js con elementos 3D interactivos, sistemas de partículas y animaciones basadas en scroll.", "project_3_title": "Chaos Ball", "project_3_description": "Un juego 3D basado en física construido con Unity, con mecánicas de cañón inteligente potenciadas por machine learning. Diseñé el terreno en Blender, desarrollé el gameplay en C#, e implementé destrucción de objetos y física de ragdoll. Usando Unity ML Agents, entrené IA para apuntar y disparar usando aprendizaje por refuerzo e imitación.", "project_4_title": "Rancho D' Ortiz – Sitio Web de Negocio Ganadero", "project_4_description": "Un sitio web empresarial limpio y multipágina construido con Next.js para un rancho. Incluye páginas de inicio, acerca de, misión, productos y servicios. Los productos y servicios se muestran con sliders Swiper para una experiencia de navegación suave y responsiva.", "project_5_title": "BB Nails and Spa Salon – Sitio Web Orientado a Citas", "project_5_description": "Un sitio web elegante y enfocado en servicios para un salón de uñas y spa, con página de inicio, listado de servicios y sección de contacto. La página de citas se integra con el sistema Square Appointment del cliente, facilitando las reservas en línea para los clientes y la edición de servicios para el cliente.", "project_6_title": "Guangopolo: El Primer Cedazo – Juego 2D Inspirado en Leyenda", "project_6_description": "Un juego 2D con temática cultural desarrollado en Unity para dar vida a una leyenda local. Lideré un equipo de 4 personas usando Scrum y diseñé múltiples niveles con mecánicas de juego únicas, todo implementado en C#.", "project_7_title": "<PERSON> Shrooms – <PERSON><PERSON> de Aventura 2D", "project_7_description": "Un juego 2D hecho a mano con cinemáticas de introducción, tutoriales guiados por NPCs, y mecánicas de combate cuerpo a cuerpo y a distancia fluidas. Construido en Unity con C#, tamb<PERSON>én incluye física dinámica de capa para el héroe, añadiendo un toque sutil de realismo al gameplay.", "project_8_title": "Project Alkawing – Supervivencia Espacial Procedural Infinita", "project_8_description": "Un juego espacial 2D de ritmo rápido construido en Unity, donde los jugadores pilotean una nave espacial a través de un universo infinito generado proceduralmente. Recolecta células de energía para mantenerte vivo, esquiva o destruye asteroides, y sobrevive tanto como puedas en el espacio profundo.", "project_9_title": "Project Arwing FR – Mundo 3D Procedural en Java y OpenGL", "project_9_description": "Un proyecto académico enfocado en navegación 3D en tiempo real a través de un entorno espacial generado proceduralmente. Desarrollado usando Java y OpenGL, con una nave espacial completamente controlable y técnicas de generación de mundo dinámico.", "about_title": "Acerca de Mí - Mi Portafolio", "about_meta_description": "Conoce mi trayectoria como desarrollador, mis antecedentes, habilidades y pasión por crear experiencias digitales innovadoras.", "about_page_title": "Acerca de Mí", "about_page_subtitle": "Desarrollador apasionado con amor por crear experiencias digitales inmersivas y empujar los límites de la tecnología web.", "about_journey_title": "<PERSON> Trayectoria", "about_journey_p1": "Mi camino hacia el desarrollo comenzó durante mis estudios de gráficos por computadora, donde descubrí la mezcla perfecta de creatividad y lógica que ofrece la programación. Lo que comenzó como curiosidad sobre cómo funciona el software rápidamente evolucionó hacia una pasión por crear experiencias digitales que inspiran y cautivan a los usuarios.", "about_journey_p2": "A lo largo de los años, he tenido el privilegio de trabajar en proyectos diversos, desde plataformas de simuladores virtuales hasta aplicaciones web dinámicas. Cada proyecto me ha enseñado algo nuevo y ha reforzado mi creencia de que la tecnología debe ser tanto poderosa como accesible.", "about_journey_p3": "Hoy me enfoco en construir software interactivo y visualmente atractivo, ya sean aplicaciones web, juegos o herramientas 3D. Disfruto trabajar en todo el stack y constantemente exploro nuevas tecnologías para mantenerme ágil y adaptable.", "about_drives_title": "Lo que me Motiva", "about_drives_p1": "Me apasiona la intersección entre tecnología y creatividad. Hay algo mágico en transformar una idea en una experiencia digital viva y respirante con la que los usuarios pueden interactuar y disfrutar.", "about_drives_p2": "Mi enfoque de desarrollo está centrado en el usuario y enfocado en el rendimiento. Creo que el gran software no solo debe verse hermoso, sino también ser rápido, accesible e intuitivo de usar. Cada línea de código que escribo está guiada por estos principios.", "about_drives_p3": "Cuando no estoy programando, me encontrarás explorando nuevas tecnologías, contribuyendo a proyectos de código abierto, o experimentando con gráficos 3D y animaciones interactivas. Siempre estoy ansioso por aprender y compartir conocimiento con la comunidad de desarrolladores.", "about_experience_title": "Experiencia Profesional", "about_technologies_title": "Tecnologías que Amo", "about_beyond_title": "Más Allá del Código", "about_3d_graphics": "Gráficos 3D", "about_3d_graphics_desc": "Explorando las posibilidades y capacidades de WebGL con Three.js, Unity 3D y Unreal para crear experiencias inmersivas.", "about_open_source": "<PERSON><PERSON><PERSON>", "about_open_source_desc": "Contribuyendo a la comunidad de desarrolladores a través de proyectos de código abierto y compartiendo conocimiento.", "about_learning": "Aprendizaje", "about_learning_desc": "Explorando constantemente nuevas tecnologías y manteniéndome actualizado con las últimas tendencias de la industria.", "about_cta_title": "Creemos Algo Increíble", "about_cta_description": "Siempre estoy emocionado de colaborar en proyectos innovadores y dar vida a ideas creativas. Ya sea que tengas una visión específica o necesites orientación en tu próxima aventura digital, me encantaría ayudar.", "about_cta_button": "Ponte en Contacto", "about_languages": "Lenguajes", "about_frontend": "Frontend", "about_backend": "Backend", "about_graphics": "Grá<PERSON><PERSON>", "about_database": "Base de Datos", "about_devops": "DevOps", "skills_title": "Habilidades - Portafolio", "skills_meta_description": "Descubre mis habilidades técnicas y experiencia en desarrollo web, gráficos 3D e ingeniería de software.", "skills_page_title": "Habilidades y Experiencia", "skills_page_subtitle": "Una visión integral de mis habilidades técnicas y áreas de especialización", "skills_technical_skills": "Habilidades Técnicas", "skills_soft_skills": "Habilidades Blandas", "skills_currently_exploring": "Explorando Actualmente", "skills_webassembly_desc": "WebAssembly (WASM) para aplicaciones web de alto rendimiento", "skills_threejs_desc": "Técnicas avanzadas de Three.js y shaders", "skills_ml_desc": "Integración de Machine Learning en aplicaciones web", "skills_edge_desc": "Computación en el borde y arquitecturas serverless", "contact_title": "Contacto - Portafolio", "contact_meta_description": "Ponte en contacto conmigo para oportunidades de colaboración, consultas de proyectos o simplemente para saludar.", "contact_page_title": "Ponte en Contacto", "contact_page_subtitle": "Colaboremos y creemos algo increíble juntos", "contact_form_title": "Envíame un mensaje", "contact_name_label": "Nombre", "contact_name_placeholder": "Tu nombre", "contact_email_label": "Correo electrónico", "contact_email_placeholder": "<EMAIL>", "contact_subject_label": "<PERSON><PERSON><PERSON>", "contact_subject_placeholder": "¿De qué se trata?", "contact_message_label": "Men<PERSON><PERSON>", "contact_message_placeholder": "Cuéntame sobre tu proyecto o simplemente saluda...", "contact_send_button": "<PERSON><PERSON><PERSON>", "contact_sending": "Enviando...", "contact_success_message": "¡Mensaje enviado exitosamente! Te responderé pronto.", "contact_error_message": "Error al enviar el mensaje. Por favor, inténtalo de nuevo.", "contact_connect_title": "Conectemos", "contact_social_links": "Encuéntrame en redes sociales", "contact_contact_info": "Información de Contacto", "contact_phone": "+****************", "contact_location": "Tu Ciudad, País", "footer_brand_description": "Desarrollador apasionado creando soluciones innovadoras con tecnologías modernas. Construyamos algo increíble juntos.", "footer_quick_links": "<PERSON><PERSON><PERSON>", "footer_contact_info": "Información de Contacto", "footer_built_with": "<PERSON><PERSON> con ❤️ usando SvelteKit y Three.js", "footer_rights_reserved": "Todos los derechos reservados"}